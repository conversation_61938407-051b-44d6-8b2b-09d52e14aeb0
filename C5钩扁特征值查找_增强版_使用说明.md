# VisionMaster 4.3.0 钩扁特征值查找增强版使用说明

## 概述

本脚本是VisionMaster 4.3.0中钩外径算法特征值匹配和图像存储功能的增强版本。相比基础版本，增强版提供了完整的配置管理、数据持久化、日志记录等企业级功能。

## 版本信息

- **文件名**: `C5钩扁特征值查找_增强版.cs`
- **版本**: 2.0 Enhanced
- **更新日期**: 2025-07-10
- **兼容性**: VisionMaster 4.3.0, .NET Framework 4.6.1

## 主要功能

### 1. 特征值匹配
- 基于角度和距离的相似度计算
- 可配置的匹配阈值
- 支持多个匹配结果排序显示

### 2. 图像存储
- 自动按日期创建文件夹
- 可配置的图像格式和文件名前缀
- 时间戳命名避免文件冲突

### 3. 数据持久化
- 特征值数据库自动保存和加载
- 支持最大记录数限制
- 文本格式存储，易于维护

### 4. 配置管理
- 文本格式配置文件
- 支持注释和动态加载
- 所有参数可自定义

### 5. 日志记录
- 详细的操作日志
- 时间戳记录
- 错误信息追踪

## 输入参数

### dangle (string)
- **描述**: 钩外径算法获取的角度参数
- **格式**: 分号分隔的数值字符串
- **示例**: `"98.893;128.893;158.892;188.893;218.893;248.893;278.894;308.892;338.894;8.895;38.894;68.894"`

### dis (string)
- **描述**: 钩外径算法获取的距离参数
- **格式**: 分号分隔的数值字符串
- **示例**: `"45.123;67.456;89.789;12.345;56.789;90.123;34.567;78.901;23.456;67.890;12.345;56.789"`

### pic (object)
- **描述**: 当前检测的图像对象
- **类型**: VisionMaster图像对象
- **用途**: 用于保存匹配结果的图像

## 配置文件

### 文件位置
配置文件名为 `HookFeatureConfig.txt`，应放置在脚本同目录下。

### 配置格式
```ini
# 钩扁特征值查找配置文件
# 匹配设置
AngleThreshold=5.0          # 角度匹配阈值（度）
DistanceThreshold=10.0      # 距离匹配阈值（像素）
SimilarityThreshold=0.8     # 相似度阈值（0.0-1.0）
MaxDisplayMatches=10        # 最大显示匹配数量

# 图像存储设置
ImageFormat=png             # 图像格式（png/bmp/jpg）
FileNamePrefix=hook_        # 文件名前缀
CreateDateFolders=true      # 是否创建日期文件夹

# 数据库设置
EnablePersistence=true      # 是否启用数据持久化
DatabaseFile=features.txt   # 数据库文件名
MaxFeatureCount=1000        # 最大特征值数量

# 日志设置
EnableLogging=true          # 是否启用日志
LogFile=hook_feature.log    # 日志文件名
```

### 配置参数说明

#### 匹配设置
- **AngleThreshold**: 角度匹配的容差范围，单位为度
- **DistanceThreshold**: 距离匹配的容差范围，单位为像素
- **SimilarityThreshold**: 相似度阈值，范围0.0-1.0，越高要求越严格
- **MaxDisplayMatches**: 最多显示的匹配结果数量

#### 图像存储设置
- **ImageFormat**: 保存图像的格式，支持png、bmp、jpg
- **FileNamePrefix**: 保存文件的前缀名称
- **CreateDateFolders**: 是否按日期创建子文件夹

#### 数据库设置
- **EnablePersistence**: 是否启用特征值数据库功能
- **DatabaseFile**: 数据库文件的名称
- **MaxFeatureCount**: 数据库中保存的最大特征值数量

#### 日志设置
- **EnableLogging**: 是否启用日志记录功能
- **LogFile**: 日志文件的名称

## 数据库格式

特征值数据库使用文本格式存储，结构如下：

```
FEATURE:
ANGLES:98.893000,128.893000,158.892000,188.893000
DISTANCES:45.123000,67.456000,89.789000,12.345000
IMAGE:D:\HookImages\2025-07-10\hook_140837_123.png
TIME:2025-07-10 14:08:37.123

FEATURE:
ANGLES:99.123000,129.456000,159.789000,189.012000
DISTANCES:46.234000,68.567000,90.890000,13.456000
IMAGE:D:\HookImages\2025-07-10\hook_140845_456.png
TIME:2025-07-10 14:08:45.456
```

## 文件结构

```
项目目录/
├── C5钩扁特征值查找_增强版.cs     # 主脚本文件
├── HookFeatureConfig.txt          # 配置文件
├── features.txt                   # 特征值数据库（自动生成）
├── hook_feature.log              # 日志文件（自动生成）
└── HookImages/                   # 图像存储目录（自动创建）
    └── 2025-07-10/              # 按日期分类的子目录
        ├── hook_140837_123.png
        ├── hook_140845_456.png
        └── ...
```

## 使用步骤

### 1. 部署脚本
1. 将 `C5钩扁特征值查找_增强版.cs` 复制到VisionMaster项目目录
2. 创建 `HookFeatureConfig.txt` 配置文件
3. 根据实际需求修改配置参数

### 2. 配置VisionMaster流程
1. 在流程中添加脚本模块
2. 设置输入变量：
   - `dangle`: 连接钩外径算法的角度输出
   - `dis`: 连接钩外径算法的距离输出  
   - `pic`: 连接当前检测图像

### 3. 运行和监控
1. 启动VisionMaster流程
2. 脚本会自动处理输入参数
3. 查看日志文件了解运行状态
4. 检查图像存储目录确认保存结果

## 输出结果

### 1. 匹配结果显示
脚本会弹出对话框显示匹配结果，包括：
- 匹配数量
- 相似度分数
- 创建时间
- 图像文件路径

### 2. 图像文件
匹配的图像会保存到指定目录，文件名格式：
`{前缀}{时间戳}.{格式}`
例如：`hook_140837_123.png`

### 3. 日志记录
详细的操作日志会记录到日志文件中，包括：
- 处理开始和结束时间
- 输入参数信息
- 匹配结果统计
- 错误信息（如有）

## 故障排除

### 常见问题

1. **配置文件不存在**
   - 脚本会使用默认配置继续运行
   - 建议创建配置文件以获得最佳体验

2. **数据库文件损坏**
   - 删除数据库文件，脚本会自动创建新的
   - 历史数据会丢失，但不影响正常运行

3. **图像保存失败**
   - 检查存储路径是否有写入权限
   - 确认磁盘空间是否充足

4. **匹配结果为空**
   - 检查相似度阈值设置是否过高
   - 确认输入参数格式是否正确

### 日志分析
通过查看日志文件可以了解：
- 脚本运行状态
- 参数解析结果
- 匹配过程详情
- 错误原因分析

## 性能优化建议

1. **数据库大小控制**
   - 合理设置MaxFeatureCount参数
   - 定期清理过期数据

2. **图像存储优化**
   - 选择合适的图像格式
   - 定期归档历史图像

3. **匹配精度调整**
   - 根据实际需求调整阈值参数
   - 平衡匹配精度和性能

## 技术支持

如遇到问题，请提供以下信息：
- VisionMaster版本信息
- 配置文件内容
- 日志文件内容
- 错误截图或描述

---

**注意**: 本文档基于VisionMaster 4.3.0编写，其他版本可能存在差异。使用前请确认版本兼容性。
