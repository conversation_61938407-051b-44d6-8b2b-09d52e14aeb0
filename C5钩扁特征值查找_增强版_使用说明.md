# VisionMaster 4.3.0 钩扁特征值查找增强版使用说明

## 概述

本脚本是VisionMaster 4.3.0中钩外径算法特征值匹配和图像存储功能的增强版本。相比基础版本，增强版提供了完整的配置管理、数据持久化、日志记录、自动目录创建等企业级功能。

## 版本信息

- **文件名**: `C5钩扁特征值查找_增强版.cs`
- **版本**: 2.1 Enhanced
- **更新日期**: 2025-07-10
- **兼容性**: VisionMaster 4.3.0, .NET Framework 4.6.1
- **新增功能**: 自动目录创建、增强的错误处理、完整的文件路径管理

## 主要功能

### 1. 特征值匹配
- 基于角度和距离的相似度计算
- 可配置的匹配阈值
- 支持多个匹配结果排序显示

### 2. 图像存储
- 自动按日期创建文件夹
- 可配置的图像格式和文件名前缀
- 时间戳命名避免文件冲突

### 3. 数据持久化
- 特征值数据库自动保存和加载
- 支持最大记录数限制
- 文本格式存储，易于维护

### 4. 配置管理
- 文本格式配置文件
- 支持注释和动态加载
- 所有参数可自定义

### 5. 日志记录
- 详细的操作日志
- 时间戳记录
- 错误信息追踪

### 6. 自动目录管理
- 首次运行自动创建所有必要目录
- 动态创建日期文件夹
- 智能路径检查和创建
- 完整的目录结构初始化

## 输入参数

### dangle (string)
- **描述**: 钩外径算法获取的角度参数
- **格式**: 分号分隔的数值字符串
- **示例**: `"98.893;128.893;158.892;188.893;218.893;248.893;278.894;308.892;338.894;8.895;38.894;68.894"`

### dis (string)
- **描述**: 钩外径算法获取的距离参数
- **格式**: 分号分隔的数值字符串
- **示例**: `"45.123;67.456;89.789;12.345;56.789;90.123;34.567;78.901;23.456;67.890;12.345;56.789"`

### pic (object)
- **描述**: 当前检测的图像对象
- **类型**: VisionMaster图像对象
- **用途**: 用于保存匹配结果的图像

## 输出参数

### matchResult (int)
- **描述**: 匹配结果状态码
- **类型**: 整数
- **用途**: 向外部系统反馈匹配处理结果和状态

#### 状态码含义：
| 状态码 | 含义 | 说明 |
|--------|------|------|
| 3 | 找到多个匹配 | 找到5个或以上匹配的特征值 |
| 2 | 找到少量匹配 | 找到2-4个匹配的特征值 |
| 1 | 找到单个匹配 | 找到1个匹配的特征值 |
| 0 | 处理成功但无匹配 | 特征值处理正常，但未找到匹配项 |
| -1 | 输入参数错误 | dangle、dis或pic参数为空或无效 |
| -2 | 特征值解析失败 | 输入字符串格式错误，无法解析为数值 |
| -3 | 图像保存失败 | 图像文件保存过程中出现错误 |
| -99 | 系统异常错误 | 脚本执行过程中发生未预期的异常 |

## 配置文件

### 文件位置
配置文件名为 `HookFeatureConfig.txt`，位于 `D:\HookFC\` 目录下。

**重要说明**：
- 脚本首次运行时会自动创建 `D:\HookFC` 目录
- 自动生成默认配置文件（如果不存在）
- 无需手动创建任何目录或文件

### 配置格式
```ini
# 钩扁特征值查找配置文件
# 匹配设置
AngleThreshold=5.0          # 角度匹配阈值（度）
DistanceThreshold=10.0      # 距离匹配阈值（像素）
SimilarityThreshold=0.8     # 相似度阈值（0.0-1.0）
MaxDisplayMatches=10        # 最大显示匹配数量

# 图像存储设置
ImageFormat=png             # 图像格式（png/bmp/jpg）
FileNamePrefix=hook_        # 文件名前缀
CreateDateFolders=true      # 是否创建日期文件夹

# 数据库设置
EnablePersistence=true      # 是否启用数据持久化
DatabaseFile=features.txt   # 数据库文件名
MaxFeatureCount=1000        # 最大特征值数量

# 日志设置
EnableLogging=true          # 是否启用日志
LogFile=hook_feature.log    # 日志文件名
```

### 配置参数说明

#### 匹配设置
- **AngleThreshold**: 角度匹配的容差范围，单位为度
- **DistanceThreshold**: 距离匹配的容差范围，单位为像素
- **SimilarityThreshold**: 相似度阈值，范围0.0-1.0，越高要求越严格
- **MaxDisplayMatches**: 最多显示的匹配结果数量

#### 图像存储设置
- **ImageFormat**: 保存图像的格式，支持png、bmp、jpg（默认png）
- **FileNamePrefix**: 保存文件的前缀名称（默认Hook_）
- **CreateDateFolders**: 是否按日期创建子文件夹（默认true）
- 图像存储位置：`D:\HookFC\Images\`

#### 数据库设置
- **EnablePersistence**: 是否启用特征值数据库功能（默认true）
- **DatabaseFile**: 数据库文件的名称（默认HookFeatures.txt）
- **MaxFeatureCount**: 数据库中保存的最大特征值数量（默认1000）
- 数据库文件位置：`D:\HookFC\HookFeatures.txt`

#### 日志设置
- **EnableLogging**: 是否启用日志记录功能（默认true）
- **LogFile**: 日志文件的名称（默认HookFeature.log）
- 日志文件位置：`D:\HookFC\HookFeature.log`

## 数据库格式

特征值数据库使用文本格式存储，结构如下：

```
FEATURE:
ANGLES:98.893000,128.893000,158.892000,188.893000
DISTANCES:45.123000,67.456000,89.789000,12.345000
IMAGE:D:\HookFC\Images\2025-07-10\Hook_140837_123.png
TIME:2025-07-10 14:08:37.123

FEATURE:
ANGLES:99.123000,129.456000,159.789000,189.012000
DISTANCES:46.234000,68.567000,90.890000,13.456000
IMAGE:D:\HookFC\Images\2025-07-10\Hook_140845_456.png
TIME:2025-07-10 14:08:45.456
```

## 文件结构

```
D:\HookFC/                        # 主目录（自动创建）
├── HookFeatureConfig.txt         # 配置文件（自动生成）
├── HookFeatures.txt              # 特征值数据库（自动生成）
├── HookFeature.log               # 日志文件（自动生成）
├── Images/                       # 图像存储目录（自动创建）
│   └── 2025-07-10/              # 按日期分类的子目录
│       ├── Hook_140837_123.bmp
│       ├── Hook_140845_456.bmp
│       └── ...
└── Reports/                      # 匹配报告目录（自动创建）
    ├── MatchResult_2025-07-10.txt
    ├── MatchResult_2025-07-11.txt
    └── ...

项目目录/
└── C5钩扁特征值查找_增强版.cs     # 主脚本文件
```

## 自动目录管理功能

### 目录创建流程
脚本采用智能的目录管理策略，确保所有必要的目录和文件都能自动创建：

#### 1. 初始化阶段（首次运行）
```
脚本启动
    ↓
创建 D:\HookFC 主目录
    ↓
创建 D:\HookFC\Images 图像存储目录
    ↓
生成默认配置文件 D:\HookFC\HookFeatureConfig.txt
    ↓
加载配置参数
    ↓
验证并创建所有配置的目录
```

#### 2. 运行时动态创建
- **日期文件夹**：每天自动创建新的日期文件夹（如 `2025-07-10`）
- **日志目录**：写入日志前自动检查并创建日志目录
- **数据库目录**：保存数据前自动检查并创建数据库目录

#### 3. 容错机制
- 目录创建失败不会中断主要功能
- 详细的错误日志记录
- 多重检查确保目录存在

### 目录权限要求
- 脚本需要对 `D:\HookFC` 目录的完整读写权限
- 如果权限不足，请以管理员身份运行VisionMaster
- 建议确保D盘有足够的存储空间

## 使用步骤

### 1. 部署脚本
1. 将 `C5钩扁特征值查找_增强版.cs` 复制到VisionMaster项目目录
2. **无需其他操作** - 脚本会自动处理所有初始化工作：
   - 自动创建 `D:\HookFC` 主目录
   - 自动创建 `D:\HookFC\Images` 图像存储目录
   - 自动生成默认配置文件 `D:\HookFC\HookFeatureConfig.txt`
   - 自动创建所有必要的子目录
3. （可选）根据实际需求修改配置参数

### 2. 配置VisionMaster流程
1. 在流程中添加脚本模块
2. 设置输入变量：
   - `dangle`: 连接钩外径算法的角度输出
   - `dis`: 连接钩外径算法的距离输出
   - `pic`: 连接当前检测图像
3. 设置输出变量：
   - `matchResult`: 匹配结果状态码，可连接到后续判断模块

### 3. 运行和监控
1. 启动VisionMaster流程
2. 脚本会自动处理所有工作：
   - 自动处理输入参数
   - 动态创建日期文件夹（如 `D:\HookFC\Images\2025-07-10\`）
   - 自动保存图像和数据
   - 记录详细日志
3. 监控方式：
   - 查看 `D:\HookFC\HookFeature.log` 了解详细运行状态
   - 检查 `D:\HookFC\Images\` 目录确认图像保存结果
   - 查看 `D:\HookFC\HookFeatures.txt` 了解特征值数据库状态
   - 查看 `D:\HookFC\Reports\MatchResult_YYYY-MM-DD.txt` 了解每日匹配统计

## 输出结果

### 1. 匹配结果显示
脚本会弹出对话框显示匹配结果，包括：
- 匹配数量
- 相似度分数
- 创建时间
- 图像文件路径

### 2. 图像文件
匹配的图像会保存到指定目录，文件名格式：
`{前缀}{时间戳}.{格式}`
例如：`hook_140837_123.png`

### 3. 日志记录
详细的操作日志会记录到日志文件中，包括：
- 处理开始和结束时间
- 输入参数信息
- 匹配结果统计
- 错误信息（如有）

### 4. 状态码输出
脚本通过`matchResult`变量输出处理状态：
- **正值（1-3）**：表示成功找到匹配，数值越大表示匹配数量越多
- **零值（0）**：表示处理成功但未找到匹配
- **负值（-1到-99）**：表示处理过程中出现错误，可用于异常处理和流程控制

### 5. 每日匹配报告
脚本自动生成每日匹配结果报告：
- **报告位置**：`D:\HookFC\Reports\MatchResult_YYYY-MM-DD.txt`
- **报告内容**：每次匹配的时间、状态码、结果描述和错误详情
- **报告格式**：`[HH:mm:ss.fff] 状态码: X | 结果: 描述 | 错误详情: 详细信息`
- **自动创建**：每天自动创建新的报告文件，便于按日期查看匹配历史

## 故障排除

### 常见问题

1. **目录创建失败**
   - **现象**：日志显示"创建目录失败"
   - **原因**：权限不足或磁盘空间不够
   - **解决方案**：
     - 以管理员身份运行VisionMaster
     - 检查D盘剩余空间
     - 手动创建 `D:\HookFC` 目录并设置完全控制权限

2. **配置文件问题**
   - **现象**：脚本使用默认配置
   - **原因**：配置文件不存在或格式错误
   - **解决方案**：
     - 脚本会自动创建默认配置文件
     - 检查 `D:\HookFC\HookFeatureConfig.txt` 格式
     - 删除配置文件让脚本重新生成

3. **数据库文件问题**
   - **现象**：特征值数据丢失
   - **原因**：数据库文件损坏或路径问题
   - **解决方案**：
     - 删除 `D:\HookFC\HookFeatures.txt`，脚本会自动创建新的
     - 检查数据库目录权限
     - 历史数据会丢失，但不影响正常运行

4. **图像保存失败（状态码-3）**
   - **现象**：图像文件未生成，状态码为-3
   - **常见原因**：
     - 权限不足：无法写入D:\HookFC目录
     - 磁盘空间不足：BMP文件较大
     - 图像对象类型不兼容：VisionMaster版本问题
     - 文件路径问题：路径过长或包含非法字符
   - **解决方案**：
     - 以管理员身份运行VisionMaster
     - 检查D盘剩余空间（建议>1GB）
     - 修改配置文件ImageFormat为jpg或png节省空间
     - 查看日志中的"图像对象类型"信息
     - 手动测试目录写入权限：`echo test > D:\HookFC\test.txt`

5. **日志文件无法写入**
   - **现象**：没有日志输出
   - **原因**：日志目录权限问题
   - **解决方案**：
     - 检查 `D:\HookFC` 目录写入权限
     - 手动创建日志文件并设置权限

6. **匹配结果为空**
   - **现象**：没有找到匹配的特征值
   - **原因**：阈值设置过严或输入格式错误
   - **解决方案**：
     - 调整配置文件中的相似度阈值
     - 检查输入参数格式（分号分隔）
     - 查看日志了解解析结果

### 日志分析和监控

#### 日志文件位置
- **主日志文件**：`D:\HookFC\HookFeature.log`
- **日志格式**：`[YYYY-MM-DD HH:mm:ss] 消息内容`

#### 关键日志信息
通过查看日志文件可以了解：

1. **系统初始化**
   ```
   [2025-07-10 14:08:30] 创建HookFC根目录: D:\HookFC
   [2025-07-10 14:08:30] 创建Images目录: D:\HookFC\Images
   [2025-07-10 14:08:30] 创建默认配置文件: D:\HookFC\HookFeatureConfig.txt
   [2025-07-10 14:08:30] 配置文件加载成功
   [2025-07-10 14:08:30] 系统初始化完成
   ```

2. **处理过程**
   ```
   [2025-07-10 14:08:35] 开始处理新的特征值
   [2025-07-10 14:08:35] 输入参数 - dangle: 143个字符, dis: 143个字符
   [2025-07-10 14:08:35] 解析得到 12 个角度值和 12 个距离值
   [2025-07-10 14:08:35] 创建日期文件夹: D:\HookFC\Images\2025-07-10
   [2025-07-10 14:08:35] 图像已保存: D:\HookFC\Images\2025-07-10\Hook_140835_123.png
   ```

3. **匹配结果**
   ```
   [2025-07-10 14:08:35] 找到 3 个匹配的特征值
   [2025-07-10 14:08:35] 新特征值已添加到数据库，当前共有 15 条记录
   [2025-07-10 14:08:35] 特征值数据库已保存，共 15 条记录
   [2025-07-10 14:08:35] 处理完成，总处理次数: 1
   ```

4. **错误信息**
   ```
   [2025-07-10 14:08:35] 创建目录失败: 拒绝访问路径"D:\HookFC"
   [2025-07-10 14:08:35] 保存图像失败: 找不到路径的一部分
   ```

#### 监控建议
- **定期检查日志大小**：避免日志文件过大影响性能
- **关注错误信息**：及时处理权限和路径问题
- **监控处理统计**：了解系统运行效率
- **备份重要日志**：保留关键的运行记录

## 性能优化建议

### 1. 存储空间管理
- **数据库大小控制**：
  - 合理设置MaxFeatureCount参数（建议500-2000）
  - 定期清理过期数据
  - 监控 `D:\HookFC\HookFeatures.txt` 文件大小

- **图像存储优化**：
  - 选择合适的图像格式（PNG平衡质量和大小）
  - 定期归档历史图像文件夹
  - 监控 `D:\HookFC\Images` 目录大小

### 2. 系统性能优化
- **目录结构优化**：
  - 启用日期文件夹功能，避免单个目录文件过多
  - 定期清理空的日期文件夹
  - 保持目录结构整洁

- **日志管理**：
  - 定期备份和清理日志文件
  - 避免日志文件过大影响写入性能
  - 可考虑按日期分割日志文件

### 3. 匹配算法优化
- **阈值参数调整**：
  - 根据实际需求调整相似度阈值
  - 平衡匹配精度和性能
  - 避免阈值过低导致误匹配

- **数据库查询优化**：
  - 控制数据库记录数量
  - 定期整理数据库文件
  - 考虑按时间范围限制查询

### 4. 系统资源监控
- **磁盘空间**：定期检查D盘剩余空间
- **内存使用**：监控VisionMaster内存占用
- **处理速度**：通过日志分析处理效率
- **错误率**：统计和分析错误日志

## 技术支持

如遇到问题，请提供以下信息：
- VisionMaster版本信息
- 配置文件内容
- 日志文件内容
- 错误截图或描述

## 快速开始指南

### 5分钟快速部署
1. **复制脚本文件**
   ```
   将 C5钩扁特征值查找_增强版.cs 复制到VisionMaster项目目录
   ```

2. **配置VisionMaster流程**
   ```
   添加脚本模块 → 设置输入变量(dangle, dis, pic) → 设置输出变量(matchResult) → 连接钩外径算法输出
   ```

3. **运行测试**
   ```
   启动流程 → 脚本自动创建D:\HookFC目录 → 查看日志确认运行状态
   ```

### 验证部署成功
运行一次后，检查以下文件和目录是否自动创建：
- ✅ `D:\HookFC\` 主目录
- ✅ `D:\HookFC\HookFeatureConfig.txt` 配置文件
- ✅ `D:\HookFC\Images\` 图像目录
- ✅ `D:\HookFC\HookFeature.log` 日志文件
- ✅ `D:\HookFC\Reports\` 报告目录
- ✅ `D:\HookFC\Images\2025-07-10\` 日期文件夹（如果启用）
- ✅ `D:\HookFC\Reports\MatchResult_2025-07-10.txt` 每日报告文件

### 常用配置调整
```ini
# 快速配置示例
SimilarityThreshold=0.7    # 降低匹配要求
MaxDisplayMatches=5        # 减少显示数量
ImageFormat=bmp           # 使用BMP保证质量
MaxFeatureCount=500       # 限制数据库大小
```

### 状态码应用示例
在VisionMaster流程中可以根据`matchResult`的值进行不同处理：
```
matchResult >= 1  → 找到匹配，继续后续处理
matchResult = 0   → 无匹配，可能需要人工检查
matchResult < 0   → 出现错误，停止流程或报警
```

### 每日报告示例
每日匹配报告文件内容示例：
```
[14:08:35.123] 状态码: 2 | 结果: 找到少量匹配（2-4个） | 错误详情: 匹配数量: 3个
[14:09:12.456] 状态码: 0 | 结果: 处理成功但无匹配 | 错误详情: 相似度阈值: 0.8
[14:10:45.789] 状态码: -1 | 结果: 输入参数错误 | 错误详情: dangle、dis或pic参数为空或无效
```

---

**注意**: 本文档基于VisionMaster 4.3.0编写，其他版本可能存在差异。使用前请确认版本兼容性。

**版本历史**:
- v2.1: 新增自动目录创建功能，增强错误处理
- v2.0: 完整的配置管理和数据持久化
- v1.0: 基础特征值匹配功能
