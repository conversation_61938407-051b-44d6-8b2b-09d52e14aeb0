using System;
using System.Text;
using System.Windows.Forms;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Xml;
using System.Xml.Serialization;
using Script.Methods;

/************************************
Shell Module default code: using .NET Framwwork 4.6.1
钩扁特征值查找增强版 - 支持配置文件和数据持久化
*************************************/
public partial class UserScript:ScriptMethods,IProcessMethods
{
    //the count of process
	//执行次数计数
    int processCount ;  
    
    // 特征值数据库，存储历史特征值
    private List<FeatureData> featureDatabase;
    
    // 配置参数
    private double angleThreshold = 5.0;  // 角度阈值
    private double distanceThreshold = 10.0;  // 距离阈值
    private double similarityThreshold = 0.8;  // 相似度阈值
    private int maxDisplayMatches = 5;  // 最大显示匹配数量
    
    // 图像存储配置
    private string imageStorageRoot = @"D:\HookImages";
    private string imageFormat = "bmp";
    private string fileNamePrefix = "Hook_";
    private bool createDateFolders = true;
    
    // 数据库配置
    private bool enablePersistence = true;
    private string databaseFile = @"D:\HookImages\FeatureDatabase.xml";
    private int maxFeatureCount = 10000;
    
    // 日志配置
    private bool enableLogging = true;
    private string logFile = @"D:\HookImages\Logs\HookFeature.log";

    /// <summary>
    /// 特征值数据结构
    /// </summary>
    [Serializable]
    public class FeatureData
    {
        public List<double> Angles { get; set; }
        public List<double> Distances { get; set; }
        public string ImagePath { get; set; }
        public DateTime CreateTime { get; set; }
        public double LastSimilarity { get; set; }  // 最后匹配的相似度
        
        public FeatureData()
        {
            Angles = new List<double>();
            Distances = new List<double>();
            CreateTime = DateTime.Now;
            LastSimilarity = 0.0;
        }
    }

    /// <summary>
    /// 特征值数据库容器（用于序列化）
    /// </summary>
    [Serializable]
    public class FeatureDatabase
    {
        public List<FeatureData> Features { get; set; }
        
        public FeatureDatabase()
        {
            Features = new List<FeatureData>();
        }
    }

    /// <summary>
    /// Initialize the field's value when compiling
	/// 预编译时变量初始化
    /// </summary>
    public void Init()
    {
        //You can add other global fields here
		//变量初始化，其余变量可在该函数中添加
        processCount = 0;
        featureDatabase = new List<FeatureData>();
        
        // 加载配置文件
        LoadConfiguration();
        
        // 确保目录存在
        EnsureDirectoriesExist();
        
        // 加载历史特征值数据
        LoadFeatureDatabase();
        
        WriteLog("系统初始化完成");
    }

    /// <summary>
    /// 加载配置文件
    /// </summary>
    private void LoadConfiguration()
    {
        try
        {
            string configFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "HookFeatureConfig.xml");
            if (File.Exists(configFile))
            {
                XmlDocument doc = new XmlDocument();
                doc.Load(configFile);
                
                // 读取匹配设置
                var matchingNode = doc.SelectSingleNode("//MatchingSettings");
                if (matchingNode != null)
                {
                    angleThreshold = GetXmlValue(matchingNode, "AngleThreshold", angleThreshold);
                    distanceThreshold = GetXmlValue(matchingNode, "DistanceThreshold", distanceThreshold);
                    similarityThreshold = GetXmlValue(matchingNode, "SimilarityThreshold", similarityThreshold);
                    maxDisplayMatches = (int)GetXmlValue(matchingNode, "MaxDisplayMatches", maxDisplayMatches);
                }
                
                // 读取图像存储设置
                var storageNode = doc.SelectSingleNode("//ImageStorage");
                if (storageNode != null)
                {
                    imageStorageRoot = GetXmlStringValue(storageNode, "RootDirectory", imageStorageRoot);
                    imageFormat = GetXmlStringValue(storageNode, "ImageFormat", imageFormat);
                    fileNamePrefix = GetXmlStringValue(storageNode, "FileNamePrefix", fileNamePrefix);
                    createDateFolders = GetXmlBoolValue(storageNode, "CreateDateFolders", createDateFolders);
                }
                
                // 读取数据库设置
                var dbNode = doc.SelectSingleNode("//Database");
                if (dbNode != null)
                {
                    enablePersistence = GetXmlBoolValue(dbNode, "EnablePersistence", enablePersistence);
                    databaseFile = GetXmlStringValue(dbNode, "DatabaseFile", databaseFile);
                    maxFeatureCount = (int)GetXmlValue(dbNode, "MaxFeatureCount", maxFeatureCount);
                }
                
                // 读取日志设置
                var logNode = doc.SelectSingleNode("//Logging");
                if (logNode != null)
                {
                    enableLogging = GetXmlBoolValue(logNode, "EnableLogging", enableLogging);
                    logFile = GetXmlStringValue(logNode, "LogFile", logFile);
                }
                
                WriteLog("配置文件加载成功");
            }
            else
            {
                WriteLog("配置文件不存在，使用默认配置");
            }
        }
        catch (Exception ex)
        {
            WriteLog("加载配置文件失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 从XML节点获取double值
    /// </summary>
    private double GetXmlValue(XmlNode parentNode, string nodeName, double defaultValue)
    {
        var node = parentNode.SelectSingleNode(nodeName);
        if (node != null && double.TryParse(node.InnerText, out double value))
        {
            return value;
        }
        return defaultValue;
    }

    /// <summary>
    /// 从XML节点获取字符串值
    /// </summary>
    private string GetXmlStringValue(XmlNode parentNode, string nodeName, string defaultValue)
    {
        var node = parentNode.SelectSingleNode(nodeName);
        return node?.InnerText ?? defaultValue;
    }

    /// <summary>
    /// 从XML节点获取布尔值
    /// </summary>
    private bool GetXmlBoolValue(XmlNode parentNode, string nodeName, bool defaultValue)
    {
        var node = parentNode.SelectSingleNode(nodeName);
        if (node != null && bool.TryParse(node.InnerText, out bool value))
        {
            return value;
        }
        return defaultValue;
    }

    /// <summary>
    /// 确保必要的目录存在
    /// </summary>
    private void EnsureDirectoriesExist()
    {
        try
        {
            if (!Directory.Exists(imageStorageRoot))
            {
                Directory.CreateDirectory(imageStorageRoot);
            }
            
            if (enableLogging)
            {
                string logDir = Path.GetDirectoryName(logFile);
                if (!Directory.Exists(logDir))
                {
                    Directory.CreateDirectory(logDir);
                }
            }
            
            if (enablePersistence)
            {
                string dbDir = Path.GetDirectoryName(databaseFile);
                if (!Directory.Exists(dbDir))
                {
                    Directory.CreateDirectory(dbDir);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show("创建目录失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 写入日志
    /// </summary>
    private void WriteLog(string message)
    {
        if (!enableLogging) return;
        
        try
        {
            string logMessage = "[" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "] " + message;
            File.AppendAllText(logFile, logMessage + Environment.NewLine);
        }
        catch
        {
            // 忽略日志写入错误
        }
    }

    /// <summary>
    /// 加载特征值数据库
    /// </summary>
    private void LoadFeatureDatabase()
    {
        if (!enablePersistence || !File.Exists(databaseFile))
        {
            WriteLog("特征值数据库文件不存在，创建新数据库");
            return;
        }
        
        try
        {
            XmlSerializer serializer = new XmlSerializer(typeof(FeatureDatabase));
            using (FileStream fs = new FileStream(databaseFile, FileMode.Open))
            {
                FeatureDatabase db = (FeatureDatabase)serializer.Deserialize(fs);
                featureDatabase = db.Features ?? new List<FeatureData>();
            }
            
            WriteLog("成功加载 " + featureDatabase.Count + " 个历史特征值");
        }
        catch (Exception ex)
        {
            WriteLog("加载特征值数据库失败: " + ex.Message);
            featureDatabase = new List<FeatureData>();
        }
    }

    /// <summary>
    /// 保存特征值数据库
    /// </summary>
    private void SaveFeatureDatabase()
    {
        if (!enablePersistence) return;
        
        try
        {
            // 如果超过最大数量，删除最旧的记录
            if (featureDatabase.Count > maxFeatureCount)
            {
                featureDatabase = featureDatabase
                    .OrderByDescending(f => f.CreateTime)
                    .Take(maxFeatureCount)
                    .ToList();
            }
            
            FeatureDatabase db = new FeatureDatabase { Features = featureDatabase };
            
            XmlSerializer serializer = new XmlSerializer(typeof(FeatureDatabase));
            using (FileStream fs = new FileStream(databaseFile, FileMode.Create))
            {
                serializer.Serialize(fs, db);
            }
            
            WriteLog("特征值数据库已保存，共 " + featureDatabase.Count + " 条记录");
        }
        catch (Exception ex)
        {
            WriteLog("保存特征值数据库失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 解析字符串特征值
    /// </summary>
    /// <param name="dataString">特征值字符串</param>
    /// <returns>特征值列表</returns>
    private List<double> ParseFeatureString(string dataString)
    {
        List<double> values = new List<double>();
        if (!string.IsNullOrEmpty(dataString))
        {
            string[] parts = dataString.Split(';');
            foreach (string part in parts)
            {
                if (double.TryParse(part, out double value))
                {
                    values.Add(value);
                }
            }
        }
        return values;
    }

    /// <summary>
    /// 计算两个特征值序列的相似度
    /// </summary>
    /// <param name="angles1">角度序列1</param>
    /// <param name="distances1">距离序列1</param>
    /// <param name="angles2">角度序列2</param>
    /// <param name="distances2">距离序列2</param>
    /// <returns>相似度分数（0-1，1为完全匹配）</returns>
    private double CalculateSimilarity(List<double> angles1, List<double> distances1,
                                     List<double> angles2, List<double> distances2)
    {
        if (angles1.Count != angles2.Count || distances1.Count != distances2.Count)
            return 0.0;

        double angleScore = 0.0;
        double distanceScore = 0.0;

        // 计算角度相似度
        for (int i = 0; i < angles1.Count; i++)
        {
            double angleDiff = Math.Abs(angles1[i] - angles2[i]);
            if (angleDiff <= angleThreshold)
            {
                angleScore += (angleThreshold - angleDiff) / angleThreshold;
            }
        }
        angleScore /= angles1.Count;

        // 计算距离相似度
        for (int i = 0; i < distances1.Count; i++)
        {
            double distanceDiff = Math.Abs(distances1[i] - distances2[i]);
            if (distanceDiff <= distanceThreshold)
            {
                distanceScore += (distanceThreshold - distanceDiff) / distanceThreshold;
            }
        }
        distanceScore /= distances1.Count;

        // 综合相似度（角度和距离各占50%权重）
        return (angleScore + distanceScore) / 2.0;
    }

    /// <summary>
    /// 保存图像到本地
    /// </summary>
    /// <param name="image">VM图像对象</param>
    /// <param name="angles">角度特征值</param>
    /// <param name="distances">距离特征值</param>
    /// <returns>保存的文件路径</returns>
    private string SaveImageToLocal(object image, List<double> angles, List<double> distances)
    {
        try
        {
            DateTime now = DateTime.Now;
            string folderPath = imageStorageRoot;

            // 根据配置决定是否创建日期文件夹
            if (createDateFolders)
            {
                string dateFolder = now.ToString("yyyy-MM-dd");
                folderPath = Path.Combine(imageStorageRoot, dateFolder);
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }
            }

            // 生成文件名
            string timeStamp = now.ToString("HHmmss_fff");
            string fileName = fileNamePrefix + timeStamp + "." + imageFormat;
            string filePath = Path.Combine(folderPath, fileName);

            // 保存图像
            if (image != null)
            {
                // 使用反射调用VM的图像保存方法
                var imageType = image.GetType();
                var saveMethod = imageType.GetMethod("SaveImage") ?? imageType.GetMethod("Save");
                if (saveMethod != null)
                {
                    saveMethod.Invoke(image, new object[] { filePath });
                    WriteLog("图像已保存: " + filePath);
                }
                else
                {
                    WriteLog("未找到图像保存方法");
                    return null;
                }
            }

            return filePath;
        }
        catch (Exception ex)
        {
            string errorMsg = "保存图像失败: " + ex.Message;
            WriteLog(errorMsg);
            MessageBox.Show(errorMsg, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return null;
        }
    }

    /// <summary>
    /// 查找匹配的特征值
    /// </summary>
    /// <param name="currentAngles">当前角度特征值</param>
    /// <param name="currentDistances">当前距离特征值</param>
    /// <returns>匹配的特征值数据列表</returns>
    private List<FeatureData> FindMatchingFeatures(List<double> currentAngles, List<double> currentDistances)
    {
        List<FeatureData> matches = new List<FeatureData>();

        foreach (var feature in featureDatabase)
        {
            double similarity = CalculateSimilarity(currentAngles, currentDistances,
                                                   feature.Angles, feature.Distances);
            if (similarity >= similarityThreshold)
            {
                feature.LastSimilarity = similarity;
                matches.Add(feature);
            }
        }

        // 按相似度排序
        matches = matches.OrderByDescending(f => f.LastSimilarity).ToList();

        WriteLog("找到 " + matches.Count + " 个匹配的特征值");
        return matches;
    }

    /// <summary>
    /// Enter the process function when running code once
	/// 流程执行一次进入Process函数
    /// </summary>
    /// <returns></returns>
    public bool Process()
    {
        try
        {
            WriteLog("开始处理新的特征值");

            // 获取输入参数
            string dangleStr = GetInput("dangle") as string;
            string disStr = GetInput("dis") as string;
            object picImage = GetInput("pic");

            if (string.IsNullOrEmpty(dangleStr) || string.IsNullOrEmpty(disStr) || picImage == null)
            {
                string errorMsg = "输入参数不完整，请检查dangle、dis和pic参数";
                WriteLog(errorMsg);
                MessageBox.Show(errorMsg, "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            WriteLog("输入参数 - dangle: " + dangleStr.Length + "个字符, dis: " + disStr.Length + "个字符");

            // 解析特征值
            List<double> currentAngles = ParseFeatureString(dangleStr);
            List<double> currentDistances = ParseFeatureString(disStr);

            if (currentAngles.Count == 0 || currentDistances.Count == 0)
            {
                string errorMsg = "特征值解析失败，请检查输入格式";
                WriteLog(errorMsg);
                MessageBox.Show(errorMsg, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }

            WriteLog("解析得到 " + currentAngles.Count + " 个角度值和 " + currentDistances.Count + " 个距离值");

            // 查找匹配的特征值
            List<FeatureData> matches = FindMatchingFeatures(currentAngles, currentDistances);

            // 保存当前图像
            string savedImagePath = SaveImageToLocal(picImage, currentAngles, currentDistances);

            if (!string.IsNullOrEmpty(savedImagePath))
            {
                // 创建新的特征值数据
                FeatureData newFeature = new FeatureData
                {
                    Angles = currentAngles,
                    Distances = currentDistances,
                    ImagePath = savedImagePath
                };

                // 添加到特征值数据库
                featureDatabase.Add(newFeature);
                WriteLog("新特征值已添加到数据库，当前共有 " + featureDatabase.Count + " 条记录");

                // 保存数据库
                SaveFeatureDatabase();

                // 输出匹配结果
                if (matches.Count > 0)
                {
                    string matchInfo = "找到 " + matches.Count + " 个匹配的特征值:\n";
                    int displayCount = Math.Min(matches.Count, maxDisplayMatches);

                    for (int i = 0; i < displayCount; i++)
                    {
                        matchInfo += "匹配 " + (i + 1) + ": 相似度 " + matches[i].LastSimilarity.ToString("F3") + ", " +
                                   "创建时间 " + matches[i].CreateTime.ToString("yyyy-MM-dd HH:mm:ss") + ", " +
                                   "图像: " + Path.GetFileName(matches[i].ImagePath) + "\n";
                    }

                    if (matches.Count > maxDisplayMatches)
                    {
                        matchInfo += "... 还有 " + (matches.Count - maxDisplayMatches) + " 个匹配结果";
                    }

                    WriteLog("匹配结果: " + matches.Count + " 个匹配项");
                    MessageBox.Show(matchInfo, "匹配结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    string noMatchMsg = "未找到匹配的特征值";
                    WriteLog(noMatchMsg);
                    MessageBox.Show(noMatchMsg, "匹配结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                MessageBox.Show("图像已保存到: " + savedImagePath, "保存成功",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

            processCount++;
            WriteLog("处理完成，总处理次数: " + processCount);
            return true;
        }
        catch (Exception ex)
        {
            string errorMsg = "处理过程中发生错误: " + ex.Message;
            WriteLog(errorMsg);
            MessageBox.Show(errorMsg, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return false;
        }
    }
}
